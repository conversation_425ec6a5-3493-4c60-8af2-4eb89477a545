const logger = require('../config/logger');
const { sequelize } = require('../models');
const { createTraceContextByReq } = require('./global.helper');

/**
 * A helper to catch asynchronous errors and optionally manage a transaction.
 *
 * @param {Function} fn - The async route handler (expects req, res, next).
 * @param {Object} [options] - Optional settings.
 * @param {boolean} [options.useTransaction=true] - If true, the handler is run in a new transaction.
 * @param {Function|null} [options.getTransaction=null] - Optional accessor function to retrieve an existing transaction from req.
 * @returns {Function} A function wrapping the original handler.
 */
const catchAsync = (fn, options = {}) => {
    const { useTransaction = true, getTransaction = null } = options;
    return async (req, res, next) => {
        if (!useTransaction) {
            // If transaction management is not desired, simply run the function.
            Promise.resolve(fn(req, res, next)).catch(next);
            return;
        }

        // Otherwise, create a new transaction.
        const transaction = await sequelize.transaction();
        req.transaction = transaction;
        if (["POST", "PUT", "PATCH", "DELETE"].includes(req.method.toUpperCase())) {
            req.traceContext = await createTraceContextByReq(req);
        }

        try {
            if (req.identity && req.body) {
                req.body.updated_by = req.identity.identity_id;
            }
            await fn(req, res, next);
            await transaction.commit();
        } catch (error) {
            // Optionally, if a getTransaction accessor is provided and returns a transaction, try to roll it back.
            if (getTransaction) {
                const existingTransaction = getTransaction(req);
                if (existingTransaction && typeof existingTransaction.rollback === 'function') {
                    try {
                        await existingTransaction.rollback();
                        logger.info('Transaction rolled back due to error.');
                    } catch (rollbackError) {
                        logger.error('Failed to rollback transaction:', rollbackError);
                    }
                }
            } else {
                // Otherwise, rollback the transaction we created.
                await transaction.rollback();
            }
            next(error);
        }
    };
};

class ApiError extends Error {
    constructor(statusCode, message, isOperational = true, stack = '') {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        if (stack) {
            this.stack = stack;
        } else {
            Error.captureStackTrace(this, this.constructor);
        }
    }
}

// Success Response Function
const sendSuccess = (res, message, code = 200, data = []) => {
    res.status(code).send({
        status: true,
        // code,
        data,
        message,
    });
};

// Error Response Function
const sendError = (res, message, code = 400, data = []) => {
    res.status(code).send({
        status: false,
        // code,
        message,
        data,
    });
};

module.exports = { ApiError, catchAsync, sendSuccess, sendError };
